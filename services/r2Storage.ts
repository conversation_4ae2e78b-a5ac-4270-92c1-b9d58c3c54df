import Constants from 'expo-constants';
import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { compressForStorage } from '@/utils/imageCompression';
import { checkR2Configuration, printR2ConfigStatus } from '@/utils/r2ConfigCheck';

// R2 Configuration from environment variables
const R2_CONFIG = {
  ACCOUNT_ID: Constants.expoConfig?.extra?.cloudflareAccountId || process.env.CLOUDFLARE_ACCOUNT_ID,
  ACCESS_KEY_ID: Constants.expoConfig?.extra?.r2AccessKeyId || process.env.R2_ACCESS_KEY_ID,
  SECRET_ACCESS_KEY: Constants.expoConfig?.extra?.r2SecretAccessKey || process.env.R2_SECRET_ACCESS_KEY,
  BUCKET_NAME: Constants.expoConfig?.extra?.r2BucketName || process.env.R2_BUCKET_NAME,
  ENDPOINT_URL: Constants.expoConfig?.extra?.r2EndpointUrl || process.env.R2_ENDPOINT_URL,
};

// Validate R2 configuration
const validateR2Config = (): boolean => {
  const required = ['ACCOUNT_ID', 'ACCESS_KEY_ID', 'SECRET_ACCESS_KEY', 'BUCKET_NAME', 'ENDPOINT_URL'];
  const missing = required.filter(key => !R2_CONFIG[key as keyof typeof R2_CONFIG]);

  if (missing.length > 0) {
    console.warn('Missing R2 configuration:', missing);
    return false;
  }

  return true;
};

// R2 Client using AWS S3 SDK (R2 is S3-compatible)
class R2Client {
  private config = R2_CONFIG;
  private s3Client: S3Client | null = null;

  private getS3Client(): S3Client {
    if (!this.s3Client) {
      if (!validateR2Config()) {
        throw new Error('R2 configuration is incomplete');
      }

      this.s3Client = new S3Client({
        region: 'auto', // R2 uses 'auto' as the region
        endpoint: this.config.ENDPOINT_URL,
        credentials: {
          accessKeyId: this.config.ACCESS_KEY_ID!,
          secretAccessKey: this.config.SECRET_ACCESS_KEY!,
        },
        // Force path-style addressing for R2 compatibility
        forcePathStyle: true,
      });
    }
    return this.s3Client;
  }

  async uploadFile(
    key: string,
    file: Blob,
    contentType: string = 'application/octet-stream'
  ): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      if (!validateR2Config()) {
        throw new Error('R2 configuration is incomplete');
      }

      console.log('Uploading to Cloudflare R2:', { key, size: file.size, type: contentType });

      const s3Client = this.getS3Client();

      // Convert Blob to ArrayBuffer for AWS SDK
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      const command = new PutObjectCommand({
        Bucket: this.config.BUCKET_NAME!,
        Key: key,
        Body: uint8Array,
        ContentType: contentType,
        // Make the object publicly readable
        ACL: 'public-read',
      });

      await s3Client.send(command);

      // Construct the public URL
      const publicUrl = `${this.config.ENDPOINT_URL}/${this.config.BUCKET_NAME}/${key}`;

      console.log('R2 upload successful:', publicUrl);

      return {
        success: true,
        url: publicUrl,
      };
    } catch (error) {
      console.error('R2 upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown upload error',
      };
    }
  }

  async deleteFile(key: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!validateR2Config()) {
        throw new Error('R2 configuration is incomplete');
      }

      console.log('Deleting from Cloudflare R2:', key);

      const s3Client = this.getS3Client();

      const command = new DeleteObjectCommand({
        Bucket: this.config.BUCKET_NAME!,
        Key: key,
      });

      await s3Client.send(command);

      console.log('R2 delete successful:', key);

      return {
        success: true,
      };
    } catch (error) {
      console.error('R2 delete error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown delete error',
      };
    }
  }
}

// Storage service interface
export interface StorageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface StorageDeleteResult {
  success: boolean;
  error?: string;
}

// Main storage service that can fallback between R2 and Supabase
export class StorageService {
  private static r2Client = new R2Client();

  // Configuration option to disable storage uploads (useful for debugging)
  private static ENABLE_STORAGE_UPLOAD = true;

  static async uploadImage(
    file: Blob,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    // Check if storage uploads are disabled
    if (!this.ENABLE_STORAGE_UPLOAD) {
      console.log('Storage uploads disabled, using data URL fallback');
      return this.uploadAsDataUrl(file, 5 * 1024 * 1024); // 5MB limit when disabled
    }

    // Try R2 first if configured
    if (validateR2Config()) {
      console.log('Uploading to Cloudflare R2...');
      const result = await this.r2Client.uploadFile(path, file, contentType);
      if (result.success) {
        return result;
      }
      console.warn('R2 upload failed, falling back to Supabase:', result.error);
    }

    // Fallback to Supabase storage
    console.log('Uploading to Supabase storage...');
    const supabaseResult = await this.uploadToSupabase(file, path, contentType);

    if (supabaseResult.success) {
      return supabaseResult;
    }

    console.warn('Supabase upload failed, trying data URL fallback:', supabaseResult.error);

    // Final fallback to data URL for small images
    const dataUrlResult = await this.uploadAsDataUrl(file, 2 * 1024 * 1024); // 2MB limit for data URLs

    if (dataUrlResult.success) {
      console.log('Successfully created data URL fallback');
      return dataUrlResult;
    }

    // If all methods fail, return the last error
    console.error('All upload methods failed');
    return {
      success: false,
      error: `All upload methods failed. Supabase: ${supabaseResult.error}, DataURL: ${dataUrlResult.error}`,
    };
  }

  /**
   * Upload image with automatic compression
   * This is the recommended method for uploading images from URIs
   */
  static async uploadImageFromUri(
    imageUri: string,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    try {
      console.log('Processing image for upload:', { imageUri, path });

      // Compress image for optimal storage
      const compressionResult = await compressForStorage(imageUri);
      console.log('Image compression result:', compressionResult);

      // Convert compressed image to blob
      const response = await fetch(compressionResult.uri);
      if (!response.ok) {
        throw new Error(`Failed to fetch compressed image: ${response.status} ${response.statusText}`);
      }

      const blob = await response.blob();
      console.log('Compressed image blob:', { size: blob.size, type: blob.type });

      // Upload the compressed blob
      return this.uploadImage(blob, path, contentType || blob.type);
    } catch (error) {
      console.error('Image processing failed, trying direct upload:', error);

      // Fallback to direct upload without compression
      try {
        const response = await fetch(imageUri);
        if (!response.ok) {
          throw new Error(`Failed to fetch original image: ${response.status} ${response.statusText}`);
        }
        const blob = await response.blob();
        return this.uploadImage(blob, path, contentType || blob.type);
      } catch (fallbackError) {
        console.error('Direct upload also failed:', fallbackError);
        return {
          success: false,
          error: `Image upload failed: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown error'}`,
        };
      }
    }
  }

  // Test Supabase storage connectivity
  private static async testSupabaseStorage(): Promise<boolean> {
    try {
      const { supabase } = await import('@/lib/supabase');

      // Try to list buckets to test connectivity
      const { data, error } = await supabase.storage.listBuckets();

      if (error) {
        console.error('Supabase storage test failed:', error);
        return false;
      }

      console.log('Supabase storage test successful, available buckets:', data?.map(b => b.name));
      return true;
    } catch (error) {
      console.error('Supabase storage test error:', error);
      return false;
    }
  }

  private static async uploadToSupabase(
    file: Blob,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    try {
      const { supabase } = await import('@/lib/supabase');

      console.log('Attempting Supabase upload:', { path, size: file.size, type: file.type });

      // Test connectivity first
      const isConnected = await this.testSupabaseStorage();
      if (!isConnected) {
        throw new Error('Supabase storage is not accessible');
      }

      const { data, error } = await supabase.storage
        .from('user-content')
        .upload(path, file, {
          contentType: contentType || file.type,
          upsert: true
        });

      if (error) {
        console.error('Supabase storage error details:', error);
        throw error;
      }

      console.log('Supabase upload successful:', data);

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('user-content')
        .getPublicUrl(path);

      console.log('Generated public URL:', publicUrl);

      return {
        success: true,
        url: publicUrl,
      };
    } catch (error) {
      console.error('Supabase upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown upload error',
      };
    }
  }

  // Fallback to data URL storage for small images
  private static async uploadAsDataUrl(
    file: Blob,
    maxSize: number = 1024 * 1024 // 1MB default
  ): Promise<StorageUploadResult> {
    try {
      if (file.size > maxSize) {
        return {
          success: false,
          error: `File too large for data URL storage (${file.size} bytes > ${maxSize} bytes)`,
        };
      }

      const reader = new FileReader();
      const dataUrl = await new Promise<string>((resolve, reject) => {
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });

      console.log('Created data URL fallback for image');

      return {
        success: true,
        url: dataUrl,
      };
    } catch (error) {
      console.error('Data URL creation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create data URL',
      };
    }
  }

  static async deleteImage(path: string): Promise<StorageDeleteResult> {
    // Try R2 first if configured
    if (validateR2Config()) {
      const result = await this.r2Client.deleteFile(path);
      if (result.success) {
        return result;
      }
    }

    // Fallback to Supabase storage
    try {
      const { supabase } = await import('@/lib/supabase');
      
      const { error } = await supabase.storage
        .from('user-content')
        .remove([path]);

      if (error) {
        throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Storage delete error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown delete error',
      };
    }
  }

  // Helper method to generate unique file paths
  static generateImagePath(userId: string, type: 'avatar' | 'scan' | 'diagnosis', extension: string = 'jpg'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${type}s/${userId}-${timestamp}-${random}.${extension}`;
  }

  // Configuration methods
  static enableStorageUploads(enabled: boolean = true): void {
    this.ENABLE_STORAGE_UPLOAD = enabled;
    console.log(`Storage uploads ${enabled ? 'enabled' : 'disabled'}`);
  }

  static isStorageUploadEnabled(): boolean {
    return this.ENABLE_STORAGE_UPLOAD;
  }

  // Health check method
  static async checkStorageHealth(): Promise<{
    r2Available: boolean;
    supabaseAvailable: boolean;
    recommendedMethod: 'r2' | 'supabase' | 'dataurl';
  }> {
    const r2Available = validateR2Config();
    const supabaseAvailable = await this.testSupabaseStorage();

    let recommendedMethod: 'r2' | 'supabase' | 'dataurl' = 'dataurl';

    if (r2Available) {
      recommendedMethod = 'r2';
    } else if (supabaseAvailable) {
      recommendedMethod = 'supabase';
    }

    console.log('Storage health check:', {
      r2Available,
      supabaseAvailable,
      recommendedMethod,
    });

    return {
      r2Available,
      supabaseAvailable,
      recommendedMethod,
    };
  }

  // Configuration check methods
  static checkR2Config() {
    return checkR2Configuration();
  }

  static printR2ConfigStatus() {
    printR2ConfigStatus();
  }
}
